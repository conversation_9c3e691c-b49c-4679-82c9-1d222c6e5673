@import "tailwindcss";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Vazirmatn', sans-serif;
  line-height: 1.6;
  color: #333;
  direction: rtl;
  background: #f8fafc;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Premium Header */
.premium-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 12px 0;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
}

.premium-header.scrolled {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98), rgba(30, 41, 59, 0.98));
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
}

.header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

/* Brand Logo */
.brand-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.brand-logo:hover {
  transform: translateY(-1px);
}

.logo-container {
  position: relative;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  overflow: hidden;
}

.logo-icon {
  font-size: 22px;
  color: white;
  z-index: 2;
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.brand-logo:hover .logo-glow {
  transform: translateX(100%);
}

.brand-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.company-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.company-tagline {
  font-size: 0.85rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Main Navigation */
.main-navigation {
  display: flex;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 4px;
  background: rgba(30, 41, 59, 0.6);
  padding: 8px;
  border-radius: 50px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  backdrop-filter: blur(10px);
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 20px;
  border-radius: 25px;
  text-decoration: none;
  color: #e2e8f0;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.nav-link:hover {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.4);
}

.nav-icon {
  font-size: 16px;
  transition: all 0.3s ease;
}

.nav-link:hover .nav-icon {
  transform: scale(1.2) rotate(5deg);
}

.nav-text {
  font-weight: 600;
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  transform: translateX(-50%);
  transition: width 0.3s ease;
}

.nav-link:hover .nav-indicator {
  width: 80%;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
}

.cta-button {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
  overflow: hidden;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(249, 115, 22, 0.5);
}

.cta-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.cta-button:hover .cta-icon {
  transform: rotate(15deg);
}

.cta-text {
  font-weight: 700;
}

.cta-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.cta-button:hover .cta-ripple {
  transform: translateX(100%);
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.mobile-menu-btn:hover {
  background: rgba(59, 130, 246, 0.8);
  transform: scale(1.05);
}

.mobile-menu-icon {
  font-size: 20px;
  color: white;
  transition: all 0.3s ease;
}

.mobile-menu-btn.active .mobile-menu-icon {
  transform: rotate(180deg);
}

/* Mobile Overlay */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98), rgba(30, 41, 59, 0.98));
  backdrop-filter: blur(20px);
  z-index: 999;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-overlay.active {
  display: block;
  top: 80px;
  animation: slideInMobile 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-navigation {
  padding: 30px 20px;
  height: 100%;
  overflow-y: auto;
}

.mobile-menu {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-item {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s ease forwards;
}

.mobile-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-item:nth-child(2) { animation-delay: 0.2s; }
.mobile-item:nth-child(3) { animation-delay: 0.3s; }
.mobile-item:nth-child(4) { animation-delay: 0.4s; }
.mobile-item:nth-child(5) { animation-delay: 0.5s; }
.mobile-item:nth-child(6) { animation-delay: 0.6s; }

.mobile-link {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px 24px;
  border-radius: 16px;
  text-decoration: none;
  color: #e2e8f0;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.mobile-link:hover {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  transform: translateX(8px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.mobile-icon {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

.mobile-cta-item {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid rgba(148, 163, 184, 0.2);
}

.mobile-cta-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  border: none;
  padding: 20px;
  border-radius: 16px;
  font-weight: 700;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
}

.mobile-cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(249, 115, 22, 0.5);
}

.mobile-cta-icon {
  font-size: 20px;
}

/* Animations */
@keyframes logoFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-3px) rotate(5deg); }
}

@keyframes slideInMobile {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  padding: 120px 0 80px 0; /* Add top padding for fixed header */
  position: relative;
  overflow: hidden;
  direction: rtl;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  direction: rtl;
}

.hero-text {
  color: white;
  text-align: right;
  direction: rtl;
  order: 1;
}

.hero-text h1 {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 25px;
  line-height: 1.3;
  text-align: right;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  direction: rtl;
}

.hero-text p {
  font-size: 1.2rem;
  margin-bottom: 35px;
  opacity: 0.95;
  line-height: 1.8;
  text-align: right;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  direction: rtl;
}

.cta-button {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
}

/* Hero Image */
.hero-image {
  position: relative;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  order: 2;
}

.hero-illustration {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Programming Icons */
.programming-icons {
  position: absolute;
  width: 100%;
  height: 100%;
}

.icon-item {
  position: absolute;
  background: white;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  font-size: 14px;
  color: #333;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.js-icon { top: 10%; left: 10%; animation-delay: 0s; }
.cpp-icon { top: 20%; right: 10%; animation-delay: 0.5s; }
.java-icon { bottom: 30%; left: 5%; animation-delay: 1s; }
.html-icon { bottom: 10%; right: 20%; animation-delay: 1.5s; background: #e34f26; color: white; }
.php-icon { top: 50%; left: 0; animation-delay: 2s; background: #777bb4; color: white; }
.code-icon { top: 30%; right: 0; animation-delay: 2.5s; background: #4f46e5; color: white; }

/* Character */
.character {
  width: 280px;
  height: 280px;
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border: 4px solid rgba(255, 255, 255, 0.1);
}

/* Character Head */
.character::before {
  content: '';
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 80px;
  background: #fbbf24;
  border-radius: 50%;
  border: 4px solid #1f2937;
  z-index: 2;
}

/* Character Glasses */
.character::after {
  content: '';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 25px;
  border: 3px solid #1f2937;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 3;
}

/* Character Body (Suit) */
.character .suit {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 140px;
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 20px 20px 60px 60px;
  z-index: 1;
}

/* Character Tie */
.character .tie {
  position: absolute;
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 80px;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  z-index: 2;
}

.laptop {
  width: 120px;
  height: 80px;
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  border-radius: 8px;
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  z-index: 4;
}

.laptop::before {
  content: '';
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  bottom: 6px;
  background: linear-gradient(135deg, #1f2937, #111827);
  border-radius: 4px;
}

.laptop::after {
  content: '< / >';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #10b981;
  font-size: 14px;
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

/* Services Section */
.services {
  padding: 60px 0;
  background: white;
  direction: rtl;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 40px;
  color: #1f2937;
  position: relative;
  direction: rtl;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-radius: 2px;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  direction: rtl;
}

.service-card {
  background: white;
  padding: 25px 20px;
  border-radius: 20px;
  text-align: center;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  direction: rtl;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.service-card:nth-child(1)::before {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.service-card:nth-child(2)::before {
  background: linear-gradient(135deg, #10b981, #059669);
}

.service-card:nth-child(3)::before {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.service-card:nth-child(4)::before {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.service-icon {
  width: 70px;
  height: 70px;
  background: #f8fafc;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 32px;
  border: 2px solid #e2e8f0;
}

.service-card:nth-child(1) .service-icon {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border-color: #f59e0b;
}

.service-card:nth-child(2) .service-icon {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  border-color: #10b981;
}

.service-card:nth-child(3) .service-icon {
  background: linear-gradient(135deg, #ede9fe, #ddd6fe);
  border-color: #8b5cf6;
}

.service-card:nth-child(4) .service-icon {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  border-color: #ef4444;
}

.service-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #1f2937;
}

.service-card p {
  color: #6b7280;
  line-height: 1.6;
  font-size: 0.9rem;
}

/* Portfolio Section */
.portfolio {
  padding: 80px 0;
  background: #f8fafc;
  direction: rtl;
}

.portfolio-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  align-items: flex-start;
  direction: rtl;
}

/* کادر سبز سمت راست */
.portfolio-text-box {
  background: white;
  border: 3px solid #10b981;
  border-radius: 20px;
  padding: 40px 30px;
  color: #1f2937;
  direction: rtl;
  text-align: right;
  position: relative;
  order: 2;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.portfolio-text-box::before {
  content: '';
  position: absolute;
  bottom: -20px;
  right: 50px;
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 20px solid #10b981;
}

.portfolio-text-box h2 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 25px;
  text-align: right;
  color: #1f2937;
}

.portfolio-text-box h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  margin-top: 20px;
  color: #1f2937;
  text-align: right;
}

.portfolio-text-box p {
  font-size: 0.95rem;
  margin-bottom: 15px;
  line-height: 1.6;
  color: #6b7280;
  text-align: right;
}

.portfolio-text-box .cta-button {
  background: #f97316;
  margin-top: 25px;
  width: 100%;
  text-align: center;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.portfolio-text-box .cta-button:hover {
  background: #ea580c;
  transform: translateY(-2px);
}

/* کارت‌های محصولات */
.portfolio-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 15px;
  order: 1;
  direction: rtl;
  max-width: 600px;
  height: 400px;
}

.portfolio-card {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 180px;
}

.portfolio-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.card-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  z-index: 2;
}

.portfolio-card.exchangrim .card-badge {
  background: #3b82f6;
}

.portfolio-card.alpha .card-badge {
  background: #8b5cf6;
}

.portfolio-card.giftinos .card-badge {
  background: #3b82f6;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* کارت اول - سمت راست، دو ردیف */
.portfolio-card.exchangrim {
  grid-column: 2;
  grid-row: 1 / 3;
  height: 100%;
}

/* کارت دوم - سمت چپ بالا */
.portfolio-card.alpha {
  grid-column: 1;
  grid-row: 1;
  height: 100%;
}

/* کارت سوم - سمت چپ پایین */
.portfolio-card.giftinos {
  grid-column: 1;
  grid-row: 2;
  height: 100%;
}

/* Technologies Section */
.technologies {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  direction: rtl;
  position: relative;
  overflow: hidden;
}

.technologies::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 1px, transparent 1px),
              radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.05) 1px, transparent 1px);
  background-size: 50px 50px, 70px 70px;
}

.tech-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.tech-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  margin-top: 15px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  direction: rtl;
  text-align: center;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
  direction: rtl;
  position: relative;
  z-index: 2;
}

.tech-card {
  background: white;
  padding: 30px 25px;
  border-radius: 20px;
  text-align: center;
  border: 2px solid #e2e8f0;
  transition: all 0.4s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  direction: rtl;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.tech-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--tech-color);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tech-card:hover::before {
  transform: scaleX(1);
}

.tech-card:hover {
  transform: translateY(-8px);
  border-color: var(--tech-color);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.tech-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tech-icon svg {
  color: var(--tech-color) !important;
}

.tech-card:hover .tech-icon {
  transform: scale(1.1);
}

.tech-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.tech-card:hover .tech-name {
  color: var(--tech-color);
}

.tech-description {
  font-size: 0.95rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* About Section */
.about {
  padding: 80px 0;
  background: white;
  direction: rtl;
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 80px;
  align-items: flex-start;
  direction: rtl;
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
  order: 2;
}

.team-illustration-img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.about-text {
  direction: rtl;
  text-align: right;
  order: 1;
}

.about-text h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 40px;
  color: #1f2937;
  line-height: 1.3;
  text-align: right;
  direction: rtl;
  position: relative;
}

.about-text h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  right: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.process-step {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  direction: rtl;
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.step-content {
  flex: 1;
  direction: rtl;
  text-align: right;
}

.step-content h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 10px;
  line-height: 1.4;
}

.step-content p {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.7;
  margin: 0;
}

/* Testimonials Section */
.testimonials {
  padding: 80px 0;
  background: #f8fafc;
  direction: rtl;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
  direction: rtl;
}

.testimonial-card {
  background: linear-gradient(135deg, #1f2937, #374151);
  color: white;
  padding: 40px;
  border-radius: 20px;
  position: relative;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
  direction: rtl;
  text-align: right;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -15px;
  left: 25px;
  font-size: 5rem;
  color: #3b82f6;
  font-weight: 700;
  opacity: 0.8;
}

.testimonial-card p {
  font-style: italic;
  margin-bottom: 25px;
  line-height: 1.7;
  font-size: 1.1rem;
  text-align: right;
  direction: rtl;
}

.testimonial-author {
  text-align: right;
  direction: rtl;
}

.testimonial-author strong {
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.testimonial-author span {
  display: block;
  color: #9ca3af;
  font-size: 0.9rem;
  margin-top: 8px;
  font-weight: 500;
}

/* Call to Action Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #1f2937, #374151);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
  direction: rtl;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
  background-size: 40px 40px, 60px 60px;
}

.cta-content {
  position: relative;
  z-index: 2;
  direction: rtl;
  text-align: center;
}

.cta-section h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  direction: rtl;
  text-align: center;
}

.cta-section p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  direction: rtl;
  text-align: center;
}

/* Modern Minimal Footer */
.exact-image-footer {
  background: #f8f9fa;
  padding: 80px 0;
  direction: rtl;
  font-family: 'Vazirmatn', sans-serif;
}

/* Footer Card Container */
.footer-card {
  background: #e9ecef;
  border-radius: 24px;
  padding: 60px 50px;
  margin: 0 auto;
  max-width: 1100px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

/* Header Section */
.footer-header {
  text-align: center;
  margin-bottom: 40px;
}

.main-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #212529;
  margin: 0;
  line-height: 1.2;
}

/* Dual Button Container */
.dual-button-container {
  display: flex;
  justify-content: center;
  margin-bottom: 50px;
}

.dual-button {
  display: flex;
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
}

.button-right {
  flex: 1;
  padding: 18px 30px;
  background: #2196f3;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1.4;
}

.button-left {
  padding: 18px 40px;
  background: #4caf50;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  min-width: 120px;
}

.button-left:hover {
  background: #45a049;
  transform: scale(1.02);
}

/* Two Column Layout */
.footer-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  margin-bottom: 45px;
  padding: 0 20px;
}

/* Links Column (Left) */
.links-column {
  text-align: right;
}

.links-column h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 30px;
}

.links-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.links-list li button {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  font-family: inherit;
  padding: 8px 0;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-end;
}

.links-list li button:hover {
  color: #2196f3;
  transform: translateX(-5px);
}

.dot {
  color: #4caf50;
  font-size: 0.8rem;
}

/* Contact Column (Right) */
.contact-column {
  text-align: right;
}

.contact-column h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 30px;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-row {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  text-align: right;
  direction: rtl;
}

.contact-row .icon {
  font-size: 1.2rem;
  min-width: 30px;
  text-align: center;
  color: #6c757d;
}

.contact-row .text {
  font-size: 1rem;
  color: #495057;
  line-height: 1.6;
  font-weight: 500;
}

/* Bottom Quote Section */
.footer-bottom {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.main-quote {
  font-size: 1rem;
  color: #555;
  margin-bottom: 20px;
  line-height: 1.7;
}

.highlight-tags {
  margin-bottom: 25px;
}

.tag {
  padding: 6px 18px;
  border-radius: 15px;
  font-weight: 600;
  font-size: 0.9rem;
  display: inline-block;
  margin: 0 5px;
}

.tag.green {
  background: #4caf50;
  color: white;
}

.tag.blue {
  background: #4a90e2;
  color: white;
}

.copyright-text {
  color: #777;
  font-size: 0.85rem;
  margin: 0;
}

.cta-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #475569;
  margin-bottom: 30px;
  text-align: center;
  direction: rtl;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.contact-btn {
  padding: 15px 30px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-family: inherit;
}

.contact-btn.primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.contact-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.contact-btn.secondary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.contact-btn.secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Quote Section */
.footer-quote {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
}

.footer-quote p {
  font-size: 1.3rem;
  color: #475569;
  margin-bottom: 15px;
  font-weight: 500;
}

.quote-highlight {
  position: relative;
  display: inline-block;
}

.highlight-text {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

/* Copyright */
.footer-copyright {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.footer-copyright p {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Header Mobile */
  .main-navigation {
    display: none;
  }

  .header-actions {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .company-name {
    font-size: 1.2rem;
  }

  .company-tagline {
    font-size: 0.75rem;
  }

  .logo-container {
    width: 42px;
    height: 42px;
  }

  .logo-icon {
    font-size: 18px;
  }

  /* Hero adjustments */
  .hero {
    padding-top: 100px; /* Account for fixed header */
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 40px;
  }

  .hero-text {
    text-align: center;
  }

  .hero-text h1 {
    font-size: 2.2rem;
    text-align: center;
  }

  .hero-text p {
    text-align: center;
  }

  .hero-image {
    height: 350px;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .portfolio-content {
    grid-template-columns: 1fr;
    gap: 40px;
    direction: rtl;
  }

  .portfolio-text-box {
    order: 1;
    text-align: center;
  }

  .portfolio-text-box::before {
    display: none;
  }

  .portfolio-images {
    order: 2;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    max-width: 100%;
    height: auto;
    direction: rtl;
  }

  .portfolio-card.exchangrim {
    grid-column: 1;
    grid-row: 1;
    height: 200px;
  }

  .portfolio-card.alpha {
    grid-column: 1;
    grid-row: 2;
    height: 180px;
  }

  .portfolio-card.giftinos {
    grid-column: 1;
    grid-row: 3;
    height: 180px;
  }

  .tech-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 20px;
  }

  .tech-card {
    padding: 25px 20px;
  }

  .tech-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tech-icon svg {
    color: var(--tech-color) !important;
  }

  .tech-name {
    font-size: 1.2rem;
  }

  .tech-description {
    font-size: 0.9rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }

  .about-text {
    text-align: center;
    order: 2;
  }

  .about-image {
    order: 1;
  }

  .about-text h2 {
    text-align: center;
  }

  .about-text h2::after {
    right: 50%;
    transform: translateX(50%);
  }

  .step-content {
    text-align: center;
  }

  .step-content h3 {
    text-align: center;
  }

  .step-content p {
    text-align: center;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .cta-section h2 {
    font-size: 2.2rem;
  }

  /* Modern Footer Mobile */
  .footer-card {
    margin: 0 20px;
    padding: 40px 30px;
    border-radius: 20px;
  }

  .main-title {
    font-size: 2rem;
    line-height: 1.3;
  }

  .dual-button {
    flex-direction: column;
    max-width: 350px;
  }

  .button-right {
    border-radius: 20px 20px 0 0;
    padding: 20px 25px;
  }

  .button-left {
    border-radius: 0 0 20px 20px;
    padding: 20px 25px;
    min-width: auto;
  }

  .footer-columns {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 0 10px;
  }

  .contact-column,
  .links-column {
    text-align: center;
  }

  .contact-row {
    justify-content: center;
    text-align: center;
  }

  .links-list li button {
    justify-content: center;
  }

  .main-quote {
    font-size: 0.95rem;
  }

  .tag {
    display: block;
    margin: 8px auto;
    width: fit-content;
  }

  .nav-menu {
    display: none;
  }

  .logo {
    position: static;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .hero-text h1 {
    font-size: 1.8rem;
  }

  .hero-text p {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .tech-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 15px;
  }

  .tech-card {
    padding: 20px 15px;
  }

  .tech-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tech-icon svg {
    color: var(--tech-color) !important;
  }

  .tech-name {
    font-size: 1.1rem;
  }

  .tech-description {
    font-size: 0.85rem;
  }

  .stats {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .cta-section h2 {
    font-size: 1.8rem;
  }

  /* Footer 480px */
  .footer-title {
    font-size: 1.5rem;
  }

  .footer-btn {
    padding: 12px 20px;
    font-size: 0.9rem;
  }

  .contact-info h3,
  .footer-links h3 {
    font-size: 1.3rem;
  }

  .quote-text {
    font-size: 0.95rem;
  }

  .highlight-green,
  .highlight-blue {
    padding: 6px 15px;
    font-size: 0.9rem;
  }
}
