'use client';

export default function Footer() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80;
      const targetPosition = element.offsetTop - headerHeight;
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <footer id="contact" className="exact-image-footer">
      <div className="container">
        <div className="footer-card">
          {/* Main Title */}
          <div className="footer-header">
            <h1 className="main-title">تیم ما آماده دریافت پروژه های شماست!</h1>
          </div>

        {/* Two-Part Button */}
        <div className="dual-button-container">
          <div className="dual-button">
            <div className="button-right blue-section">
              همین حالا با مشاوران ما تماس برقرار کنید
            </div>
            <button className="button-left green-section">
              تماس
            </button>
          </div>
        </div>

        {/* Two Column Layout */}
        <div className="footer-columns">
          {/* Left Column - Links */}
          <div className="links-column">
            <h3>لینک های مهم</h3>
            <ul className="links-list">
              <li>
                <button onClick={() => scrollToSection('home')}>
                  <span className="dot">●</span>
                  بلاگ
                </button>
              </li>
              <li>
                <button onClick={() => scrollToSection('portfolio')}>
                  <span className="dot">●</span>
                  محصولات
                </button>
              </li>
              <li>
                <button onClick={() => scrollToSection('services')}>
                  <span className="dot">●</span>
                  خدمات
                </button>
              </li>
              <li>
                <button onClick={() => scrollToSection('contact')}>
                  <span className="dot">●</span>
                  تماس با ما
                </button>
              </li>
            </ul>
          </div>

          {/* Right Column - Contact Info */}
          <div className="contact-column">
            <h3>تماس با ما</h3>
            <div className="contact-list">
              <div className="contact-row">
                <span className="icon">📞</span>
                <span className="text">۰۲۱۹۱۶۲۰۸۳ - ۰۲۱۹۱۶۲۰۸۴</span>
              </div>
              <div className="contact-row">
                <span className="icon">📍</span>
                <span className="text">تهران، قیطریه، خیابان بهار ستان، خیابان بیست، پلاک ۱۱</span>
              </div>
              <div className="contact-row">
                <span className="icon">✉️</span>
                <span className="text"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Quote */}
        <div className="footer-bottom">
          <p className="main-quote">
            " آرمین افق داده گستر" چابکی که ایده‌های شما به راهکارهای نرم‌افزاری پیشرفته تبدیل می‌شوند. ما تیمی از متخصصین نوآور در مسیر رشد و تحول دیجیتال کسب‌وکار شما هستیم
          </p>
          <div className="highlight-tags">
            <span className="tag green">از ایده تا سودآوری، لحظه به لحظه</span>
            <span className="tag blue">همراه تان خواهیم بود</span>
          </div>
          <p className="copyright-text">
            تمامی حقوق مادی و معنوی برای آرمین افق داده گستر محفوظ است.
          </p>
        </div>
        </div>
      </div>
    </footer>
  );
}
