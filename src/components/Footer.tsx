'use client';

export default function Footer() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80;
      const targetPosition = element.offsetTop - headerHeight;
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <footer id="contact" className="exact-image-footer">
      <div className="container">
        {/* Main Title with Number */}
        <div className="footer-header">
          <div className="title-number">1</div>
          <h1 className="main-title">تیم ما آماده دریافت پروژه های شماست!</h1>
        </div>

        {/* CTA Buttons */}
        <div className="cta-buttons">
          <button className="cta-btn green">تماس</button>
          <button className="cta-btn blue">همین حالا با مشاوران ما تماس برقرار کنید</button>
        </div>

        {/* Two Column Layout */}
        <div className="footer-columns">
          {/* Right Column - Contact Info */}
          <div className="contact-column">
            <h3>تماس با ما</h3>
            <div className="contact-list">
              <div className="contact-row">
                <span className="icon">📞</span>
                <span className="text">۰۲۱۹۱۶۲۰۸۳ - ۰۲۱۹۱۶۲۰۸۴</span>
              </div>
              <div className="contact-row">
                <span className="icon">📍</span>
                <span className="text">تهران، قیطریه، خیابان بهار ستان، خیابان بیست، پلاک ۱۱</span>
              </div>
              <div className="contact-row">
                <span className="icon">✉️</span>
                <span className="text"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Left Column - Links */}
          <div className="links-column">
            <h3>لینک های مهم</h3>
            <ul className="links-list">
              <li>
                <button onClick={() => scrollToSection('home')}>
                  <span className="dot">●</span>
                  بلاگ
                </button>
              </li>
              <li>
                <button onClick={() => scrollToSection('portfolio')}>
                  <span className="dot">●</span>
                  محصولات
                </button>
              </li>
              <li>
                <button onClick={() => scrollToSection('services')}>
                  <span className="dot">●</span>
                  خدمات
                </button>
              </li>
              <li>
                <button onClick={() => scrollToSection('contact')}>
                  <span className="dot">●</span>
                  تماس با ما
                </button>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Quote */}
        <div className="footer-bottom">
          <p className="main-quote">
            " آرمین افق داده گستر" چابکی که ایده‌های شما به راهکارهای نرم‌افزاری پیشرفته تبدیل می‌شوند. ما تیمی از متخصصین نوآور در مسیر رشد و تحول دیجیتال کسب‌وکار شما هستیم
          </p>
          <div className="highlight-tags">
            <span className="tag green">از ایده تا سودآوری، لحظه به لحظه</span>
            <span className="tag blue">همراه تان خواهیم بود</span>
          </div>
          <p className="copyright-text">
            تمامی حقوق مادی و معنوی برای آرمین افق داده گستر محفوظ است.
          </p>
        </div>
      </div>
    </footer>
  );
}
