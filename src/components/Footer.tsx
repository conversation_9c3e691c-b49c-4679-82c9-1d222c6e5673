'use client';

export default function Footer() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80;
      const targetPosition = element.offsetTop - headerHeight;
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <footer id="contact" className="image-footer">
      <div className="container">
        <div className="footer-content">
          {/* Main Title with Number */}
          <div className="footer-title-section">
            <h1 className="footer-title">تیم ما آماده دریافت پروژه های شماست!</h1>
            <div className="title-number">1</div>
          </div>

          {/* CTA Buttons */}
          <div className="footer-buttons">
            <button className="footer-btn primary">
              تماس
            </button>
            <button className="footer-btn secondary">
              همین حالا با مشاوران ما تماس برقرار کنید
            </button>
          </div>

          {/* Main Content */}
          <div className="footer-main-content">
            {/* Contact Info */}
            <div className="contact-info">
              <h3>تماس با ما</h3>
              <div className="contact-items">
                <div className="contact-item">
                  <span className="contact-icon">📞</span>
                  <span>۰۲۱۹۱۶۲۰۸۳ - ۰۲۱۹۱۶۲۰۸۴</span>
                </div>
                <div className="contact-item">
                  <span className="contact-icon">📍</span>
                  <span>تهران، قیطریه، خیابان بهار ستان، خیابان بیست، پلاک ۱۱</span>
                </div>
                <div className="contact-item">
                  <span className="contact-icon">✉️</span>
                  <span><EMAIL></span>
                </div>
              </div>
            </div>

            {/* Links */}
            <div className="footer-links">
              <h3>لینک های مهم</h3>
              <ul>
                <li>
                  <button onClick={() => scrollToSection('home')}>
                    <span className="bullet">●</span>
                    بلاگ
                  </button>
                </li>
                <li>
                  <button onClick={() => scrollToSection('portfolio')}>
                    <span className="bullet">●</span>
                    محصولات
                  </button>
                </li>
                <li>
                  <button onClick={() => scrollToSection('services')}>
                    <span className="bullet">●</span>
                    خدمات
                  </button>
                </li>
                <li>
                  <button onClick={() => scrollToSection('contact')}>
                    <span className="bullet">●</span>
                    تماس با ما
                  </button>
                </li>
              </ul>
            </div>
          </div>

          {/* Quote Section */}
          <div className="footer-quote">
            <p className="quote-text">
              " آرمین افق داده گستر" چابکی که ایده‌های شما به راهکارهای نرم‌افزاری پیشرفته تبدیل می‌شوند. ما تیمی از متخصصین نوآور در مسیر رشد و تحول دیجیتال کسب‌وکار شما هستیم
            </p>
            <div className="quote-highlight">
              <span className="highlight-green">از ایده تا سودآوری، لحظه به لحظه</span>
              <span className="highlight-blue">همراه تان خواهیم بود</span>
            </div>
            <p className="copyright">
              تمامی حقوق مادی و معنوی برای آرمین افق داده گستر محفوظ است.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
